<!-- 用于创建/编辑会议 -->
<template>
    <div>
        <el-dialog
            title="预定会议室"
            :visible.sync="dialogVisible"
            width="95%"
            top="5vh"
        >
            <div class="query-container">
                <el-date-picker
                    class="mr-10"
                    v-model="date"
                    placeholder="请选择日期"
                    ref="dataPicker"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :clearable="false"
                >
                </el-date-picker>
                <el-button
                    type="primary"
                    @click="handleQuickAccess"
                    class="mr-10"
                    >今天</el-button
                >
                <el-time-picker
                    class="time-picker mr-10"
                    size="small"
                    is-range
                    v-model="timeRange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围"
                    format="HH:mm"
                    value-format="HH:mm"
                    clearable
                >
                </el-time-picker>
                <el-input
                    class="mr-10"
                    v-model="meetingName"
                    placeholder="请输入会议名称"
                    style="width: 70%"
                    type="mini"
                />
                <el-button type="primary" @click="save">确认预定</el-button>
            </div>
            <div class="room-booking-table">
                <el-table
                    :data="roomData"
                    style="width: 100%"
                    :header-cell-style="{
                        background: '#4A90E2',
                        color: '#fff',
                        textAlign: 'center'
                    }"
                    max-height="450"
                    :cell-class-name="getCellClassName"
                    @selection-change="handleSelectionChange"
                >
                    <!-- 选择列 -->
                    <el-table-column
                        type="selection"
                        width="55"
                        align="center"
                    ></el-table-column>

                    <!-- 会议室列 -->
                    <el-table-column
                        prop="roomName"
                        label="会议室"
                        min-width="200"
                        :showOverflowTooltip="true"
                    ></el-table-column>

                    <!-- 时间段列组 - 每个小时一个组，包含00和30两个子列 -->
                    <el-table-column
                        v-for="hour in hours"
                        :key="hour"
                        :label="hour.toString()"
                        align="center"
                    >
                        <!-- 00分钟子列 -->
                        <el-table-column
                            label="00"
                            min-width="34"
                            align="center"
                        >
                            <template slot-scope="scope">
                                <el-popover
                                    v-if="
                                        !isTimeAvailable(
                                            scope.row,
                                            `${hour}_00`
                                        )
                                    "
                                    placement="top"
                                    trigger="hover"
                                    :content="
                                        getMeetingTextInfo(
                                            scope.row,
                                            `${hour}_00`
                                        )
                                    "
                                    :open-delay="100"
                                >
                                    <template slot="reference">
                                        <div
                                            :class="
                                                getTimeSlotClass(
                                                    scope.row,
                                                    `${hour}_00`
                                                )
                                            "
                                            class="time-cell"
                                        ></div>
                                    </template>
                                    <div class="meeting-popover">
                                        <div class="meeting-info-item">
                                            <span class="label"
                                                >会议名称：</span
                                            >
                                            <span class="value">{{
                                                getMeetingDisplayName(
                                                    scope.row,
                                                    `${hour}_00`
                                                )
                                            }}</span>
                                        </div>
                                        <div class="meeting-info-item">
                                            <span class="label"
                                                >预定时间：</span
                                            >
                                            <span class="value">{{
                                                getMeetingDisplayTime(
                                                    scope.row,
                                                    `${hour}_00`
                                                )
                                            }}</span>
                                        </div>
                                        <div class="meeting-info-item">
                                            <span class="label">预定人：</span>
                                            <span class="value">{{
                                                getMeetingDisplayCaller(
                                                    scope.row,
                                                    `${hour}_00`
                                                )
                                            }}</span>
                                        </div>
                                    </div>
                                </el-popover>
                                <div
                                    v-else
                                    :class="
                                        getTimeSlotClass(
                                            scope.row,
                                            `${hour}_00`
                                        )
                                    "
                                    class="time-cell"
                                ></div>
                            </template>
                        </el-table-column>

                        <!-- 30分钟子列 -->
                        <el-table-column
                            label="30"
                            min-width="34"
                            align="center"
                        >
                            <template slot-scope="scope">
                                <el-popover
                                    v-if="
                                        !isTimeAvailable(
                                            scope.row,
                                            `${hour}_30`
                                        )
                                    "
                                    placement="top"
                                    trigger="hover"
                                    :content="
                                        getMeetingTextInfo(
                                            scope.row,
                                            `${hour}_30`
                                        )
                                    "
                                    :open-delay="100"
                                >
                                    <template slot="reference">
                                        <div
                                            :class="
                                                getTimeSlotClass(
                                                    scope.row,
                                                    `${hour}_30`
                                                )
                                            "
                                            class="time-cell"
                                        ></div>
                                    </template>
                                    <div class="meeting-popover">
                                        <div class="meeting-info-item">
                                            <span class="label"
                                                >会议名称：</span
                                            >
                                            <span class="value">{{
                                                getMeetingDisplayName(
                                                    scope.row,
                                                    `${hour}_30`
                                                )
                                            }}</span>
                                        </div>
                                        <div class="meeting-info-item">
                                            <span class="label"
                                                >预定时间：</span
                                            >
                                            <span class="value">{{
                                                getMeetingDisplayTime(
                                                    scope.row,
                                                    `${hour}_30`
                                                )
                                            }}</span>
                                        </div>
                                        <div class="meeting-info-item">
                                            <span class="label">预定人：</span>
                                            <span class="value">{{
                                                getMeetingDisplayCaller(
                                                    scope.row,
                                                    `${hour}_30`
                                                )
                                            }}</span>
                                        </div>
                                    </div>
                                </el-popover>
                                <div
                                    v-else
                                    :class="
                                        getTimeSlotClass(
                                            scope.row,
                                            `${hour}_30`
                                        )
                                    "
                                    class="time-cell"
                                ></div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'MeetingQuality',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        meetingId: {
            type: String,
            default: ''
        },
        // 外部传入的会议名称
        name: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            date: moment().format('YYYY-MM-DD'),
            // 小时数组
            hours: [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
            timeRange: '',
            meetingName: '',
            selectedRows: [],
            conflictData: [],
            // 会议室数据
            roomData: [],
            // 会议数据
            meetingsData: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        async dialogVisible(newVal) {
            if (newVal) {
                this.initData();
                this.initRoomAvailability();
                await Promise.all([this.getMeetingInfo(), this.getRoomData()]);
                this.updateRoomAvailability();
            }
        },
        date(newVal) {
            if (newVal) {
                this.update();
            }
        }
    },
    methods: {
        /**
         * 解析地址字符串为数组
         * @param {String|Array} address - 地址字符串或数组
         * @returns {Array} 地址数组
         */
        parseAddresses(address) {
            if (!address) return [];
            if (Array.isArray(address)) return address;
            return address.split(',').map((addr) => addr.trim());
        },

        initData() {
            this.meetingName = this.name;
        },
        closeDialog() {
            this.dialogVisible = false;
        },

        // 初始化数据
        async update() {
            this.initRoomAvailability();
            await this.getMeetingInfo();
            this.updateRoomAvailability();
        },
        async getRoomData() {
            const api = this.$service.feature.meetingRoom.getRoomList;
            const params = {};
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.roomData = res.body.map((i) => ({
                    ...i,
                    roomName: i.name
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        async getMeetingInfo() {
            const api = this.$service.feature.meetingRoom.getMeetingInfo;
            const params = {
                beginDate: this.date,
                endDate: this.date
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.meetingsData = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },

        // 初始化会议室可用性
        initRoomAvailability() {
            this.roomData.forEach((room) => {
                // 确保 availability 对象存在
                if (!room.availability) {
                    this.$set(room, 'availability', {});
                }
                // 初始化所有时间段为可用
                this.hours.forEach((hour) => {
                    this.$set(room.availability, `${hour}_00`, true);
                    this.$set(room.availability, `${hour}_30`, true);
                });
            });
        },

        // 更新会议室可用性
        updateRoomAvailability() {
            // 重置所有时间段为可用
            this.initRoomAvailability();

            // 清空所有会议室的会议数据，避免重复添加
            this.roomData.forEach((room) => {
                this.$set(room, 'meetings', []);
            });

            // 根据会议数据标记占用的时间段
            this.meetingsData.forEach((meeting) => {
                if (
                    meeting.beginDate === this.date &&
                    meeting.endDate === this.date
                ) {
                    // 处理会议室地址可能包含多个地址的情况（用英文逗号分隔）
                    const meetingAddresses = this.parseAddresses(
                        meeting.address
                    );

                    meetingAddresses.forEach((roomId) => {
                        const room = this.roomData.find(
                            (r) => r.id.toString() === roomId.trim()
                        );

                        if (room) {
                            const occupiedSlots = this.getOccupiedTimeSlots(
                                meeting.beginTime,
                                meeting.endTime
                            );

                            occupiedSlots.forEach((slot) => {
                                this.$set(room.availability, slot, false);
                            });

                            // 存储会议信息到房间数据中
                            room.meetings.push(meeting);
                        }
                    });
                }
            });
        },

        // 根据开始和结束时间获取占用的时间段
        getOccupiedTimeSlots(oriBeginTime, oriEndTime) {
            const slots = [];

            const startTime = moment(oriBeginTime, 'HH:mm');
            const endTime = moment(oriEndTime, 'HH:mm');

            const current = startTime.clone();
            const minutes = current.minute();

            if (minutes > 0) {
                if (minutes >= 30) {
                    current.minute(30).second(0);
                } else {
                    current.minute(0).second(0);
                }
            } else {
                current.second(0);
            }

            while (current.isBefore(endTime)) {
                const hour = current.hour();
                const minute = current.minute();

                if (this.hours.includes(hour)) {
                    slots.push(`${hour}_${minute.toString().padStart(2, '0')}`);
                }

                current.add(30, 'minutes');
            }

            return slots;
        },

        // 检查时间段是否可用
        isTimeAvailable(room, timeSlot) {
            // 确保 availability 对象存在，如果不存在则认为时间可用
            if (!room.availability) {
                return true;
            }
            return room.availability[timeSlot] !== false;
        },

        // 获取时间段的CSS类
        getTimeSlotClass(room, timeSlot) {
            const classes = [];

            if (!this.isTimeAvailable(room, timeSlot)) {
                classes.push('occupied');
            } else {
                classes.push('available');
            }

            return classes;
        },

        /**
         * 检查多个会议室的冲突
         * @returns {Promise<{hasConflict: boolean, conflicts: Array}>} 冲突检查结果
         */
        async checkMultipleRoomsConflict() {
            try {
                const conflictsByRoom = {};

                // 检查选中的会议室在指定时间段是否有冲突
                for (const room of this.selectedRows) {
                    if (!this.timeRange || this.timeRange.length === 0) {
                        continue;
                    }

                    const [startTime, endTime] = this.timeRange;
                    const roomConflicts = this.meetingsData.filter(
                        (meeting) => {
                            return this.checkMeetingTimeConflict(
                                meeting,
                                room.id.toString(),
                                startTime,
                                endTime
                            );
                        }
                    );

                    if (roomConflicts.length > 0) {
                        conflictsByRoom[room.id] = {
                            roomName: room.roomName,
                            conflicts: roomConflicts
                        };
                    }
                }

                const allConflicts = Object.values(conflictsByRoom);

                return {
                    hasConflict: allConflicts.length > 0,
                    conflicts: allConflicts
                };
            } catch (error) {
                console.error('检查会议室冲突失败:', error);
                throw error;
            }
        },

        /**
         * 检查单个会议是否与新时间冲突
         * @param {Object} meeting - 会议信息
         * @param {String} roomId - 会议室ID
         * @param {String} startTime - 新会议开始时间
         * @param {String} endTime - 新会议结束时间
         * @returns {Boolean} 是否冲突
         */
        checkMeetingTimeConflict(meeting, roomId, startTime, endTime) {
            // 处理会议室地址可能包含多个地址的情况（用英文逗号分隔）
            const meetingAddresses = this.parseAddresses(meeting.address);
            const hasAddressConflict = meetingAddresses.includes(roomId);

            // 只检查同一会议室的会议
            if (!hasAddressConflict) {
                return false;
            }

            // 检查日期是否匹配
            if (
                meeting.beginDate !== this.date ||
                meeting.endDate !== this.date
            ) {
                return false;
            }

            // 获取会议时间
            const meetingStartTime = meeting.beginTime;
            const meetingEndTime = meeting.endTime;

            if (!meetingStartTime || !meetingEndTime) {
                return false;
            }

            const meetingStart = this.timeToMinutes(meetingStartTime);
            const meetingEnd = this.timeToMinutes(meetingEndTime);
            const newStart = this.timeToMinutes(startTime);
            const newEnd = this.timeToMinutes(endTime);

            // 时间重叠检查
            return !(newEnd <= meetingStart || newStart >= meetingEnd);
        },

        /**
         * 将时间字符串转换为分钟数
         * @param {string} timeStr - 时间字符串，格式为 HH:mm
         * @returns {number} 转换后的分钟数
         */
        timeToMinutes(timeStr) {
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        },

        /**
         * 将时间段转换为时间字符串
         * @param {string} timeSlot - 时间段，格式如 "9_00" 或 "14_30"
         * @returns {string} 格式化的时间字符串，如 "09:00"
         */
        timeSlotToTime(timeSlot) {
            const [hour, minute] = timeSlot.split('_');
            return `${hour.padStart(2, '0')}:${minute}`;
        },

        // 保存预订
        async save() {
            try {
                if (this.selectedRows.length === 0) {
                    this.$message.warning('请选择至少一个会议室');
                    return;
                }

                if (!this.timeRange || this.timeRange.length === 0) {
                    this.$message.warning('请选择时间范围');
                    return;
                }

                if (!this.meetingName || this.meetingName.trim() === '') {
                    this.$message.warning('请输入会议名称');
                    return;
                }

                // 检查冲突
                const conflictResult = await this.checkMultipleRoomsConflict();
                if (conflictResult.hasConflict) {
                    this.$alert(
                        `${conflictResult.conflicts
                            .map((i) => `[${i.roomName}] 冲突`)
                            .join('<br/>')}`,
                        '会议室冲突不能提交',
                        {
                            dangerouslyUseHTMLString: true
                        }
                    );
                    return;
                }

                // 构建预订参数
                const { timeRange, meetingName } = this;
                const address = this.selectedRows
                    .map((room) => room.id)
                    .join(',');

                const params = {
                    address,
                    beginDate: this.date,
                    endDate: this.date,
                    beginTime: timeRange[0],
                    endTime: timeRange[1],
                    meetingName,
                    remindType: '',
                    repeatType: '',
                    isInterval: '',
                    desc: ''
                };

                // 调用保存接口
                const api = this.$service.feature.meetingRoom.bookRoom;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (isNaN(Number(res.body))) {
                    this.$message.error('会议室冲突');
                    return;
                }
                this.$message.success('保存成功');
                this.$emit(
                    'success',
                    this.selectedRows.map((i) => i.name)
                );
                this.closeDialog();
            } catch (error) {
                console.error('保存失败:', error);
                this.$message.error('保存失败，请重试');
            }
        },
        handleQuickAccess() {
            this.date = moment().format('YYYY-MM-DD');
        },

        // 获取单元格样式
        getCellClassName({ columnIndex }) {
            // 前两列是选择列和会议室列，从第三列开始都是时间列
            if (columnIndex >= 2) {
                return 'row-time-cell';
            }
            return '';
        },

        // 根据时间段获取对应的会议信息
        getMeetingByTimeSlot(room, timeSlot) {
            if (!room.meetings || room.meetings.length === 0) {
                return null;
            }

            // 查找包含该时间段的会议
            return room.meetings.find((meeting) => {
                const { beginTime, endTime } = meeting;

                // 获取该会议占用的所有时间段
                const occupiedSlots = this.getOccupiedTimeSlots(
                    beginTime,
                    endTime
                );

                // 检查当前时间段是否在占用的时间段列表中
                return occupiedSlots.includes(timeSlot);
            });
        },

        // 获取会议信息文本（用于简单的 content 属性）
        getMeetingTextInfo(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            if (!meeting) {
                return '暂无会议信息';
            }

            return `会议名称: ${meeting.name}\n预定时间: ${meeting.beginTime} - ${meeting.endTime}\n预定人: ${meeting.callerLastName}`;
        },

        // 获取会议名称显示
        getMeetingDisplayName(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            return meeting && meeting.name ? meeting.name : '未知会议';
        },

        // 获取会议时间显示
        getMeetingDisplayTime(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            if (!meeting) {
                return '00:00 - 00:00';
            }
            const beginTime = meeting.beginTime || '00:00';
            const endTime = meeting.endTime || '00:00';
            return `${beginTime} - ${endTime}`;
        },

        // 获取会议预定人显示
        getMeetingDisplayCaller(room, timeSlot) {
            const meeting = this.getMeetingByTimeSlot(room, timeSlot);
            return meeting && meeting.callerLastName
                ? meeting.callerLastName
                : '未知';
        },
        handleSelectionChange(rows) {
            this.selectedRows = rows;
        },

        /**
         * 获取指定会议室的选中时间段
         * @param {Object} room - 会议室对象
         * @returns {Array} 选中的时间段数组
         */
        getSelectedTimeSlots(room) {
            if (!this.timeRange || this.timeRange.length === 0) {
                return [];
            }

            const [startTime, endTime] = this.timeRange;
            const selectedSlots = [];

            try {
                // 获取时间范围内的所有时间段
                const occupiedSlots = this.getOccupiedTimeSlots(
                    startTime,
                    endTime
                );

                // 过滤出可用的时间段
                occupiedSlots.forEach((slot) => {
                    if (this.isTimeAvailable(room, slot)) {
                        selectedSlots.push(slot);
                    }
                });

                return selectedSlots;
            } catch (error) {
                console.error('获取选中时间段失败:', error);
                return [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.mr-10 {
    margin-right: 10px;
}
.query-container {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.room-booking-table {
    .time-cell {
        width: 100%;
        height: 36px;

        display: flex;
        align-items: center;
        justify-content: center;

        &.occupied {
            cursor: pointer;
            background-color: #ff5252;
        }
    }
}
::v-deep .el-table__cell.row-time-cell {
    padding: 0 !important;
    .cell {
        height: auto;
        padding: 0;
    }
}

.meeting-popover {
    .meeting-info-item {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        &:last-child {
            margin-bottom: 0;
        }

        .label {
            font-weight: 500;
            color: #606266;
            min-width: 70px;
            flex-shrink: 0;
        }

        .value {
            color: #303133;
            flex: 1;
        }
    }
}
</style>
