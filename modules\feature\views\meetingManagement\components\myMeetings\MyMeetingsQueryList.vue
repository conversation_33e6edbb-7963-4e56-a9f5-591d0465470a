<template>
    <div class="my-meetings-query-container">
        <div class="query-container">
            <!-- 查询选项 -->
            <div class="query-options">
                <el-date-picker
                    v-model="week"
                    type="week"
                    placeholder="选择周"
                    ref="dataPicker"
                    :format="dateFormat"
                    :clearable="false"
                    :picker-options="pickerOptions"
                >
                </el-date-picker>
                <el-button
                    type="primary"
                    @click="handleQuickAccess('本周')"
                    class="quick-access"
                    >本周</el-button
                >
                <el-button type="primary" @click="handleQuickAccess('下周')"
                    >下周</el-button
                >
            </div>
            <div>
                <el-button
                    type="primary"
                    class="fixed-button"
                    @click="registerUnavailableTime"
                    >登记不可用时间</el-button
                >
                <el-button
                    type="primary"
                    class="fixed-button"
                    @click="createMeeting"
                    >创建会议</el-button
                >
            </div>
        </div>
        <!-- 表格 -->
        <el-table
            class="my-meetings-query"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center'
            }"
            :row-style="{ height: '35px!important' }"
            empty-text="无会议数据"
        >
            <el-table-column align="left" prop="meetingTitle" label="会议名称">
                <template slot-scope="scope">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="scope.row.meetingTitle"
                        placement="top"
                    >
                        <el-button
                            class="ellipsis-text"
                            type="text"
                            @click="handleMeetingClick(scope.row)"
                        >
                            {{ scope.row.meetingTitle }}
                        </el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="类型"
                width="100"
                prop="meetingType"
            >
            </el-table-column>
            <el-table-column
                prop="organizerName"
                label="会议组织者"
                align="center"
                width="80"
            >
            </el-table-column>
            <el-table-column
                v-for="(i, index) in daysOfWeek"
                :label="i"
                :key="i"
                align="center"
                width="80"
            >
                <el-table-column
                    :key="weekList[index]"
                    :label="weekList[index]"
                    align="center"
                    width="80"
                >
                    <template slot-scope="scope">
                        {{ scope.row[daysOfWeek[index]] }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column prop="其他" label="其他" align="center" width="80">
            </el-table-column>
        </el-table>
        <MeetingUpdate
            :visible.sync="meetingUpdateVisible"
            title="创建会议"
            @update="updateData"
        ></MeetingUpdate>
        <RegistUnavailableTimeDialog
            :visible.sync="registUnavailableTimeDialogVisible"
        ></RegistUnavailableTimeDialog>
    </div>
</template>

<script>
import moment from 'moment';
import { getDaysOfWeek } from '../../commonFunction';
import MeetingUpdate from 'feature/views/meetingManagement/components/modals/meetingUpdate';
import RegistUnavailableTimeDialog from './RegistUnavailableTimeDialog.vue';

// 设置一周的开始为周一，不设置的话，默认是周末
moment.updateLocale('zh-cn', {
    week: {
        dow: 1
    }
});

const weekList = [
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
    '星期日'
];

export default {
    name: 'MyMeetingsQueryList',
    components: { MeetingUpdate, RegistUnavailableTimeDialog },
    props: {
        // 当前激活的页签名称
        activeTab: {
            type: String,
            default: ''
        },
        // 刷新触发器
        shouldRefresh: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            week: new Date(),
            pickerOptions: {
                firstDayOfWeek: 1
            },
            tableList: [],
            weekList,
            meetingUpdateVisible: false,
            registUnavailableTimeDialogVisible: false
        };
    },
    computed: {
        // 展示的格式
        dateFormat() {
            const startOfWeek = moment(this.week)
                .startOf('week')
                .format('YYYY/M/D');
            const endOfWeek = moment(this.week)
                .endOf('week')
                .format('YYYY/M/D');
            return `${startOfWeek} - ${endOfWeek}`;
        },
        // 一周内的时间，例如'1月1日'
        daysOfWeek() {
            return getDaysOfWeek(this.week);
        }
    },
    watch: {
        week(newVal) {
            newVal && this.getMeetingList();
        },
        // 监听页签切换和刷新触发器
        shouldRefresh(newVal, oldVal) {
            // 只有当前页签是"我的会议"且刷新计数器发生变化时才执行查询
            if (this.activeTab === 'MyMeetings' && newVal !== oldVal) {
                this.getMeetingList();
            }
        }
    },
    created() {
        this.getMeetingList();
    },
    methods: {
        /**
         * 点击快捷按钮的处理
         * @param {String} type 按钮类型
         */
        handleQuickAccess(type) {
            const currentDate = new Date();
            if (type === '本周') {
                this.week = currentDate;
            } else if (type === '下周') {
                const nextWeekDate = new Date();
                nextWeekDate.setDate(currentDate.getDate() + 7);
                this.week = nextWeekDate;
            }
        },

        /**
         * 获取周会议列表数据
         */
        async getMeetingList() {
            const api = this.$service.feature.myMeetings.list;
            try {
                const startOfWeek = moment(this.week)
                    .startOf('week')
                    .format('YYYY-MM-DD');
                const endOfWeek = moment(this.week)
                    .endOf('week')
                    .format('YYYY-MM-DD');

                const res = await api({
                    startDate: startOfWeek,
                    endDate: endOfWeek
                });

                if (res.head.code === '000000') {
                    this.handleMeetingList(res.body);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },

        /**
         * 处理会议列表数据结构
         * @param {Array} data 原始会议列表数据
         */
        handleMeetingList(data) {
            // 将每个日期作为key
            const dateObj = [...getDaysOfWeek(this.week), '其他'].reduce(
                (acc, date) => {
                    acc[date] = '';
                    return acc;
                },
                {}
            );
            this.tableList = data.map((item) => {
                const res = { ...item, ...dateObj };
                res[item.dateVal] = item.meetingStartTime;
                return res;
            });
        },

        handleMeetingClick(row) {
            this.$router.push({
                path: 'meetingDetail',
                query: { id: row.meetingId }
            });
        },

        /**
         * 刷新数据 - 供父组件调用
         */
        refreshData() {
            this.getMeetingList();
        },

        /**
         * 创建会议
         */
        createMeeting() {
            this.meetingUpdateVisible = true;
        },
        /**
         * 弹窗关闭后更新数据
         */
        updateData() {
            this.getMeetingList();
        },
        /**
         * 登记不可用时间
         */
        registerUnavailableTime() {
            this.registUnavailableTimeDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.my-meetings-query-container {
    padding-bottom: 10px;

    .query-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .query-options {
        display: flex;
        .quick-access {
            margin-left: 10px;
        }
    }
}

.ellipsis-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    max-width: 100%;
    height: 23px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
.my-meetings-query {
    border: 1px solid #8c8c8c !important;
}
</style>
