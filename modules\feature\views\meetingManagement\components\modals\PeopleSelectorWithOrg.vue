<template>
    <el-dialog
        title="选择人员"
        :visible.sync="dialogVisible"
        width="1000px"
        :close-on-click-modal="false"
        top="5vh"
        @close="handleClose"
    >
        <div class="people-tree-container">
            <!-- 全部人员根节点 -->
            <div class="tree-node root-node">
                <div class="node-content">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        :value="checkAll"
                        @change="handleCheckAllChange"
                    >
                        全部人员
                    </el-checkbox>
                </div>

                <!-- 部门列表 -->
                <div class="children-container">
                    <div
                        v-for="org in orgList"
                        :key="org.id"
                        class="tree-node org-node"
                    >
                        <div class="node-content">
                            <el-checkbox
                                :indeterminate="getOrgIndeterminate(org)"
                                :value="isOrgSelected(org)"
                                @change="toggleOrgSelection(org)"
                            >
                                {{ org.label }}
                            </el-checkbox>
                        </div>

                        <!-- 人员网格 -->
                        <div class="people-grid-container">
                            <div class="people-grid">
                                <div
                                    v-for="person in getOrgPeople(org.id)"
                                    :key="person.id"
                                    class="person-item"
                                    @click="togglePersonSelection(person)"
                                >
                                    <el-checkbox
                                        :value="isPersonSelected(person)"
                                        @change="togglePersonSelection(person)"
                                        class="person-checkbox"
                                    />
                                    <span class="person-name">{{
                                        person.name
                                    }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">
                确 定 ({{ selectedPeople.length }})
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'PeopleSelectorWithOrg',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 已选择的人员列表
        value: {
            type: Array,
            default: () => []
        },
        // 是否多选
        multiple: {
            type: Boolean,
            default: true
        },
        roleType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            orgList: [],
            allPeopleList: [],
            selectedPeople: [],
            // 使用Set提高查找性能
            selectedPeopleSet: new Set(),
            checkAll: false,
            isIndeterminate: false,
            // 缓存计算结果
            orgPeopleCache: {},
            orgSelectedCache: {},
            orgIndeterminateCache: {}
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        },

        // 组织人员映射，用于快速查找
        orgPeopleMap() {
            return this.orgPeopleCache || {};
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.initData();
            }
        },
        value: {
            immediate: true,
            handler(newVal) {
                // 如果allPeopleList已经加载完成，则处理选中人员
                if (this.allPeopleList.length > 0) {
                    this.processSelectedPeople();
                    this.updateSelectedPeopleSet();
                } else {
                    // 如果数据还没加载，先保存传入的值
                    this.selectedPeople = [...(newVal || [])];
                }
            }
        },
        selectedPeople() {
            this.updateCheckAllStatus();
            this.clearCache();
        }
    },
    methods: {
        async initData() {
            await this.getAllPeopleList();
            // 数据加载完成后，重新处理传入的选中人员
            this.processSelectedPeople();
            this.updateSelectedPeopleSet();
        },

        async getAllPeopleList() {
            const params = {};
            const api = this.$service.feature.employee.getPeopleListWithOrg;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.processRawData(res.body);
            } catch (error) {
                console.error('获取人员列表失败:', error);
            }
        },

        // 处理原始数据，转换为组件需要的格式
        processRawData(rawData) {
            // 生成组织列表
            this.orgList = rawData.map((org, index) => ({
                id: `org_${index}`,
                label: org.orgName,
                name: org.orgName
            }));

            // 生成扁平化的人员列表
            this.allPeopleList = [];
            this.orgPeopleCache = {};

            rawData.forEach((org, orgIndex) => {
                const orgId = `org_${orgIndex}`;
                const orgPeople = org.accountNameList.map((person) => ({
                    id: `${org.orgName}_${person.account}`,
                    account: person.account,
                    name: person.name,
                    orgId,
                    orgName: org.orgName
                }));

                this.allPeopleList.push(...orgPeople);
                this.orgPeopleCache[orgId] = orgPeople;
            });
        },

        // 处理传入的选中人员，将其与内部数据格式匹配
        processSelectedPeople() {
            if (!this.value || this.value.length === 0) {
                return;
            }

            // 根据传入的account在allPeopleList中找到匹配的人员
            const matchedPeople = [];
            this.value.forEach((inputPerson) => {
                const foundPerson = this.allPeopleList.find(
                    (person) => person.account === inputPerson.account
                );
                if (foundPerson) {
                    matchedPeople.push(foundPerson);
                }
            });

            this.selectedPeople = matchedPeople;
        },

        // 更新选中人员的Set集合
        updateSelectedPeopleSet() {
            this.selectedPeopleSet.clear();
            this.selectedPeople.forEach((person) => {
                this.selectedPeopleSet.add(person.id);
            });
        },

        // 清除缓存
        clearCache() {
            this.orgSelectedCache = {};
            this.orgIndeterminateCache = {};
        },

        updateCheckAllStatus() {
            const checkedCount = this.selectedPeople.length;
            this.checkAll = checkedCount === this.allPeopleList.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.allPeopleList.length;
        },

        // 优化全选操作，使用批量更新
        handleCheckAllChange(val) {
            if (val) {
                // 使用Set快速检查，避免重复添加
                const newSelected = [];
                const existingIds = new Set(
                    this.selectedPeople.map((p) => p.id)
                );

                this.allPeopleList.forEach((person) => {
                    if (!existingIds.has(person.id)) {
                        newSelected.push(person);
                    }
                });

                this.selectedPeople = [...this.selectedPeople, ...newSelected];
            } else {
                this.selectedPeople = [];
            }
            this.updateSelectedPeopleSet();
        },

        // 使用Set提高查找性能
        isPersonSelected(person) {
            return this.selectedPeopleSet.has(person.id);
        },

        togglePersonSelection(person) {
            if (this.isPersonSelected(person)) {
                this.removeSelectedPerson(person);
            } else if (!this.multiple) {
                this.selectedPeople = [person];
                this.updateSelectedPeopleSet();
            } else {
                this.selectedPeople.push(person);
                this.selectedPeopleSet.add(person.id);
            }
        },

        removeSelectedPerson(person) {
            const index = this.selectedPeople.findIndex(
                (p) => p.id === person.id
            );
            if (index > -1) {
                this.selectedPeople.splice(index, 1);
                this.selectedPeopleSet.delete(person.id);
            }
        },

        clearAllSelected() {
            this.selectedPeople = [];
            this.selectedPeopleSet.clear();
        },

        // 使用缓存的组织人员映射
        getOrgPeople(orgId) {
            return this.orgPeopleMap[orgId] || [];
        },

        // 使用缓存优化组织选择状态计算
        isOrgSelected(org) {
            if (this.orgSelectedCache[org.id] !== undefined) {
                return this.orgSelectedCache[org.id];
            }

            const orgPeople = this.getOrgPeople(org.id);
            const result =
                orgPeople.length > 0 &&
                orgPeople.every((person) => this.isPersonSelected(person));

            this.orgSelectedCache[org.id] = result;
            return result;
        },

        // 使用缓存优化组织半选状态计算
        getOrgIndeterminate(org) {
            if (this.orgIndeterminateCache[org.id] !== undefined) {
                return this.orgIndeterminateCache[org.id];
            }

            const orgPeople = this.getOrgPeople(org.id);
            const selectedCount = orgPeople.filter((person) =>
                this.isPersonSelected(person)
            ).length;

            const result =
                selectedCount > 0 && selectedCount < orgPeople.length;
            this.orgIndeterminateCache[org.id] = result;
            return result;
        },

        // 优化组织选择切换，批量操作
        toggleOrgSelection(org) {
            const orgPeople = this.getOrgPeople(org.id);
            const isSelected = this.isOrgSelected(org);

            if (isSelected) {
                // 批量取消选择该组织下的所有人员
                const peopleToRemove = orgPeople.filter((person) =>
                    this.isPersonSelected(person)
                );

                peopleToRemove.forEach((person) => {
                    const index = this.selectedPeople.findIndex(
                        (p) => p.id === person.id
                    );
                    if (index > -1) {
                        this.selectedPeople.splice(index, 1);
                        this.selectedPeopleSet.delete(person.id);
                    }
                });
            } else {
                // 批量选择该组织下的所有人员
                const peopleToAdd = orgPeople.filter(
                    (person) => !this.isPersonSelected(person)
                );

                this.selectedPeople.push(...peopleToAdd);
                peopleToAdd.forEach((person) => {
                    this.selectedPeopleSet.add(person.id);
                });
            }

            // 清除相关缓存
            delete this.orgSelectedCache[org.id];
            delete this.orgIndeterminateCache[org.id];
        },

        handleClose() {
            // 清空所有选择项
            this.clearAllSelected();
            this.dialogVisible = false;
        },

        handleConfirm() {
            this.$emit('input', this.selectedPeople);
            this.$emit('confirm', this.selectedPeople, this.roleType);
            this.handleClose();
        }
    }
};
</script>

<style lang="scss" scoped>
.people-tree-container {
    height: 60vh;
    overflow-y: auto;
}

.tree-node {
    .node-content {
        display: flex;
        align-items: center;
        padding: 8px 0;
    }

    &.org-node {
        .node-content {
            padding-left: 20px;
        }
    }
}

.people-grid-container {
    padding: 8px 0 8px 40px;

    .people-grid {
        display: grid;
        grid-template-columns: repeat(10, 1fr);
        gap: 6px;

        .person-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            height: 32px;
            font-size: 12px;

            .person-checkbox {
                margin-right: 6px;
            }

            .person-name {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}

.dialog-footer {
    text-align: right;
    padding-top: 16px;
}
</style>
