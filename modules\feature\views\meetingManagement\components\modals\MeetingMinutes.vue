<!-- 用于创建/编辑会议 -->
<template>
    <div>
        <el-dialog
            :title="`${title} ${
                form.minutesStatus ? `${form.minutesStatus}` : ''
            }`"
            :visible.sync="dialogVisible"
            width="85%"
            :before-close="reset"
            :cell-style="{ verticalAlign: 'top' }"
        >
            <el-form
                v-if="meetingClass === '评审会议'"
                class="meeting-conclusion"
                ref="conclusionForm"
                :model="form"
            >
                <el-form-item
                    label="评审结论"
                    prop="meetingConclusion"
                    :rules="required"
                >
                    <el-radio-group
                        v-model="form.meetingConclusion"
                        @input="handleConclusionInput"
                        :disabled="isMinutesPass && !dataManagerPermission"
                    >
                        <el-radio label="通过"></el-radio>
                        <el-radio label="让步通过"></el-radio>
                        <el-radio label="不通过"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="评审意见"
                    prop="meetingConclusionView"
                    :rules="required"
                >
                    <el-input
                        v-model="form.meetingConclusionView"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入补充意见"
                        maxlength="500"
                        :disabled="isMinutesPass && !dataManagerPermission"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div class="flex">
                <el-upload
                    class="uploader"
                    :action="url"
                    :show-file-list="false"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="validateExcelFile"
                    :auto-upload="true"
                    name="file"
                    :headers="headers"
                >
                    <el-button
                        size="small"
                        type="primary"
                        v-show="
                            form.minutesStatus === '' ||
                            form.minutesStatus === '撰写中' ||
                            form.minutesStatus === '审核中' ||
                            dataManagerPermission
                        "
                        >导入</el-button
                    >
                </el-upload>
                <el-button
                    type="primary"
                    @click="addMinutes"
                    class="add-minutes"
                    v-show="
                        form.minutesStatus === '' ||
                        form.minutesStatus === '撰写中' ||
                        form.minutesStatus === '审核中' ||
                        dataManagerPermission
                    "
                    >新增</el-button
                >
            </div>
            <el-form
                ref="meetingMinutesForm"
                :model="form"
                class="minutes-form"
            >
                <el-table
                    :data="form.meetingMinutesList"
                    class="minutes-table"
                    :row-style="{ background: '#fff' }"
                    empty-text="无会议纪要"
                >
                    <el-table-column
                        prop="prop"
                        label="会议纪要"
                        align="center"
                    >
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="problemItem"
                        >
                            <template #header>
                                <RedStar class="required" />问题/事项
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.problemItem`"
                                    :rules="required"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="500"
                                        v-model="scoped.row.problemItem"
                                        :disabled="
                                            !dataManagerPermission &&
                                            isMinutesPass
                                        "
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="creatorAccount"
                            label="提出人"
                            width="110"
                        >
                            <template #header>
                                <RedStar class="required" />提出人
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.creatorAccount`"
                                    :rules="required"
                                >
                                    <PeopleSelector
                                        class="input-outline input-outline input-outline input-outline"
                                        placeholder=""
                                        v-model="scoped.row.creatorAccount"
                                        :ref="`proposer${scoped.$index}`"
                                        :clearable="false"
                                        :disabled="
                                            !dataManagerPermission &&
                                            isMinutesPass
                                        "
                                        :options="meetingPeopleOptions"
                                    ></PeopleSelector>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="meetingRequire"
                            label="会议要求"
                        >
                            <template #header>
                                <RedStar class="required" />会议要求
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.meetingRequire`"
                                    :rules="required"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="500"
                                        v-model="scoped.row.meetingRequire"
                                        :disabled="
                                            !dataManagerPermission &&
                                            isMinutesPass
                                        "
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="responsibility"
                            label="责任人"
                            width="110"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.responsibility`"
                                >
                                    <PeopleSelector
                                        placeholder=""
                                        class="input-outline input-outline input-outline input-outline"
                                        v-model="scoped.row.responsibleAccount"
                                        :ref="`responsible${scoped.$index}`"
                                        :clearable="false"
                                        :options="externalOptions"
                                        @input="
                                            handleResponsibleChange(
                                                scoped.$index,
                                                $event
                                            )
                                        "
                                    ></PeopleSelector>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 注意这里下面几个的表单校验规则要动态控制 -->
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="completionTime"
                            label="计划完成时间"
                            width="110"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.completionTime`"
                                >
                                    <el-date-picker
                                        v-show="
                                            scoped.row.responsibleAccount
                                                .length !== 0
                                        "
                                        class="input-outline input-outline input-outline input-outline"
                                        type="date"
                                        placeholder=""
                                        v-model="scoped.row.planFinishDate"
                                        value-format="yyyy-MM-dd"
                                    ></el-date-picker>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="status"
                            label="状态"
                            width="100"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.status`"
                                >
                                    <el-select
                                        v-show="
                                            scoped.row.responsibleAccount
                                                .length !== 0
                                        "
                                        v-model="scoped.row.finishStatus"
                                        class="input-outline"
                                    >
                                        <el-option
                                            v-for="item in CONSTANTS.TASK_STATUS"
                                            :label="item"
                                            :key="item"
                                            :value="item"
                                        ></el-option
                                    ></el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="finishDesc"
                            label="完成情况"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`meetingMinutesList.${scoped.$index}.completionSituation`"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="2000"
                                        v-model="scoped.row.finishDesc"
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            label="操作"
                            width="80"
                            v-if="!isMinutesPass || dataManagerPermission"
                        >
                            <template #default="scoped">
                                <el-button
                                    type="danger"
                                    @click="deleteRow(scoped.$index)"
                                    >删除</el-button
                                >
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-if="showWrongMessage"
                            header-align="center"
                            align="center"
                            label="错误信息"
                            width="180"
                        >
                            <template #default="scoped">
                                {{ handlerrorMessage(scoped.row.errMsgList) }}
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-form>
            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button
                    type="primary"
                    @click="save('保存草稿')"
                    v-if="
                        form.minutesStatus === '' ||
                        form.minutesStatus === '撰写中' ||
                        form.minutesStatus === '审核中'
                    "
                    >保存草稿</el-button
                >
                <el-button
                    v-if="
                        form.minutesStatus === '' ||
                        form.minutesStatus === '撰写中' ||
                        form.minutesStatus === '审核中'
                    "
                    type="primary"
                    @click="save('提交审核')"
                    >保存并提交审核</el-button
                >
                <el-button
                    v-if="
                        title === '审核会议纪要' &&
                        form.minutesStatus === '审核中'
                    "
                    type="primary"
                    @click="save('审核通过')"
                    >审核通过</el-button
                >
                <el-button
                    v-if="getIsWriterAndReviewer && !isMinutesPass"
                    type="primary"
                    @click="save('审核通过')"
                    >保存并通过审核</el-button
                >
                <el-button
                    v-if="isMinutesPass || dataManagerPermission"
                    type="primary"
                    @click="save('保存')"
                    >修改会议纪要</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';
import { getSelectedLabel } from 'feature/views/meetingManagement/commonFunction';
import RedStar from 'feature/components/redStar';
import { encryptByMd5 } from 'wtf-core-vue/src/utils/crypto';
import { randomStr, randomTime } from 'wtf-core-vue/src/methods/signature';
import {
    isWriterAndReviewer,
    getMeetingPeopleOptions,
    isOrganizerOrWriter
} from '../../commonFunction';

const random = randomStr(13);
const ranTime = randomTime();
const final =
    random +
    ranTime +
    encryptByMd5(
        `${random}&${ranTime}axeonmc&/console/v1/boss/productservice/fileUpload&POST`
    );

const form = {
    meetingConclusion: '',
    meetingConclusionView: '',
    meetingMinutesList: [
        {
            creatorAccount: '',
            creatorName: '',
            finishDate: '',
            finishDesc: '',
            finishStatus: '',
            meetingId: '',
            meetingRequire: '',
            planFinishDate: '',
            problemItem: '',
            responsibleAccount: [],
            responsibleName: ''
        }
    ],
    minutesReviewDate: '',
    minutesStatus: '',
    minutesWriteDate: ''
};
export default {
    name: 'MeetingMinutes',
    components: { PeopleSelector, RedStar },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        meetingId: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            form: {
                ...this.$tools.cloneDeep(form),
                meetingId: this.meetingId
            },
            showWrongMessage: false,
            CONSTANTS,
            headerInfo: {
                meetingInfo: { meetingTitle: '' }
            },
            // 会议结论
            meetingConclusion: '',
            required: {
                required: true,
                // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            },
            // 会议数据管理员专有的编辑权限
            dataManagerPermission:
                this.$store.state.permission.btnDatas.includes(
                    'MeetingDataManagerEditButton'
                ),
            headers: {
                Accept: 'application/json, text/plain, */*',
                Authorization: `Bearer ${this.$tools.getToken()}`,
                signature: final
            }
        };
    },
    computed: {
        // 会议归类
        meetingClass() {
            if (
                this.headerInfo.meetingInfo.meetingType === '一级TR评审' ||
                this.headerInfo.meetingInfo.meetingType === '二级TR评审'
            ) {
                return '评审会议';
            }
            return '一般会议';
        },
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 导入会议纪要
        url() {
            return this.$service.feature.meetingMinutes.import();
        },
        // 责任人的选项
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        },
        // 由参会人员组成的下拉列表(提出人的选项)
        meetingPeopleOptions() {
            return getMeetingPeopleOptions(this.headerInfo);
        },
        // 组织者或编写人是否有编辑权限（在恰当的时机）
        isOrganizerOrWriterHasPermission() {
            if (
                this.headerInfo.meetingInfo.meetingStatus === '已取消' ||
                (this.headerInfo.meetingInfo.meetingStatus === '结束' &&
                    this.headerInfo.meetingInfo.minutesStatus === '无纪要')
            ) {
                return false;
            }
            const bool = isOrganizerOrWriter(
                this.headerInfo.meetingPartRelateList
            );
            return bool;
        },
        // 会议纪要是否审核通过
        isMinutesPass() {
            return (
                this.headerInfo.meetingInfo.minutesStatus === '任务未关闭' ||
                this.headerInfo.meetingInfo.minutesStatus === '任务已关闭'
            );
        },
        // 是否同时是会议纪要审核人和会议纪要编制人
        getIsWriterAndReviewer() {
            return isWriterAndReviewer(this.headerInfo.meetingPartRelateList);
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getMeetingInfo();
                this.getMeetingMinutesInfo();
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
        },
        /**
         * 评审组织形式修改时的变更
         * @param {String} value 当前值
         */
        handleIsOnlineChange(value) {
            if (value === '线上') {
                this.form.meetingLocation = '项目管理信息化平台';
            } else {
                this.form.meetingLocation = '';
            }
        },
        /**
         * 删除项
         * @param {Number} index 序号
         * @param {String} type 评委列表或者参会人员列表
         */
        decrease(index, type) {
            this.form[type].splice(index, 1);
        },
        /**
         * 新增项
         * @param {String} type 评委列表或者参会人员列表
         */
        increase(type) {
            this.form[type].push({
                // 评委角色
                meetingRole: '',
                // 评委姓名
                userAccount: '',
                // 参会情况
                attendanceStatus: '',
                // 替代人
                replaceUserAccount: ''
            });
        },
        /**
         * 保存
         * @param {String} operateName 操作名称
         */
        async save(operateName) {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            this.editMeetingMintes(operateName);
        },
        /**
         * 新增纪要
         */
        addMinutes() {
            this.form.meetingMinutesList.push({
                creatorAccount: '',
                creatorName: '',
                finishDate: '',
                finishDesc: '',
                finishStatus: '',
                meetingId: '',
                meetingRequire: '',
                planFinishDate: '',
                problemItem: '',
                responsibleAccount: [],
                responsibleName: ''
            });
        },
        /**
         * 删除行
         * @param {Number} index 序号
         */
        deleteRow(index) {
            this.$confirm('确定删除吗?', '提示', { type: 'warning' }).then(
                () => {
                    this.form.meetingMinutesList.splice(index, 1);
                }
            );
        },
        /**
         * 获取会议信息
         */
        async getMeetingInfo() {
            const api = this.$service.feature.meeting.getMeetingInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.headerInfo = res.body;
                    const { meetingInfo } = this.headerInfo;
                    this.form.minutesStatus = meetingInfo.minutesStatus;
                    this.form.meetingConclusion =
                        meetingInfo.meetingConclusion || '';
                    this.form.meetingConclusionView =
                        meetingInfo.meetingConclusionView || '';
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取会议纪要信息
         */
        async getMeetingMinutesInfo() {
            const api = this.$service.feature.meetingMinutes.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.handleBackendData(res.body);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 编辑会议纪要
         * @param {String} operateName 操作名称
         */
        async editMeetingMintes(operateName) {
            const data = this.handleFormData(operateName);
            const api = this.$service.feature.meetingMinutes.edit;
            try {
                const res = await api(data);
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    if (operateName !== '保存草稿') {
                        this.closeDialog();
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 将表单数据转为后端的格式
         * @param {String} operateName 操作名称
         * @returns {Object} 转换后的数据
         */
        handleFormData(operateName) {
            const data = this.$tools.cloneDeep(this.form);
            data.operateName = operateName;
            data.meetingInfoVo = this.headerInfo;
            data.meetingInfoVo.meetingInfo.meetingConclusion =
                data.meetingConclusion;
            data.meetingInfoVo.meetingInfo.meetingConclusionView =
                data.meetingConclusionView;
            let isAllFinish = true;
            data.meetingMinutesList.forEach((i, index) => {
                if (i.responsibleAccount.length === 0) {
                    i.planFinishDate = '';
                    i.finishStatus = '';
                }
                if (i.finishStatus === '未完成' || !i.finishStatus) {
                    isAllFinish = false;
                }
                if (Array.isArray(i.creatorAccount)) {
                    i.creatorAccount = i.creatorAccount.join(' ');
                    i.creatorName = getSelectedLabel(
                        this.$refs[`proposer${index}`]
                    ).join(' ');
                }
                if (Array.isArray(i.responsibleAccount)) {
                    i.responsibleAccount = i.responsibleAccount.join(' ');
                    i.responsibleName = getSelectedLabel(
                        this.$refs[`responsible${index}`]
                    ).join(' ');
                }
            });
            if (operateName === '保存草稿' || operateName === '提交审核') {
                data.meetingInfoVo.meetingInfo.minutesWriteDate = moment(
                    new Date()
                ).format('YYYY-MM-DD');
                data.meetingInfoVo.meetingInfo.minutesStatus =
                    operateName === '保存草稿' ? '撰写中' : '审核中';
            } else if (operateName === '审核通过') {
                const curDate = moment(new Date()).format('YYYY-MM-DD');
                data.meetingInfoVo.meetingInfo.minutesReviewDate = curDate;
                // 如果审核时没有会议纪要编制日期，也加上
                if (!data.meetingInfoVo.meetingInfo.minutesWriteDate) {
                    data.meetingInfoVo.meetingInfo.minutesWriteDate = curDate;
                }
                data.meetingInfoVo.meetingInfo.minutesStatus = isAllFinish
                    ? '任务已关闭'
                    : '任务未关闭';
                // 评审组织形是线上，审核通过时，会将时间更新为审核通过的时间
                if (data.meetingInfoVo.meetingInfo.organizeForm === '线上') {
                    const curTime = moment(new Date()).format('HH:mm');
                    data.meetingInfoVo.meetingInfo.startDate = curDate;
                    data.meetingInfoVo.meetingInfo.startTime = curTime;
                }
            } else if (operateName === '保存' && this.isMinutesPass) {
                data.meetingInfoVo.meetingInfo.minutesStatus = isAllFinish
                    ? '任务已关闭'
                    : '任务未关闭';
            }
            return data;
        },
        /**
         * 处理后端接口返回的数据
         * @param {Object} data 接口返回的原始数据
         */
        handleBackendData(data) {
            data.forEach((i) => {
                if (i.creatorAccount) {
                    i.creatorAccount = i.creatorAccount.split(' ');
                }
                if (i.responsibleAccount) {
                    i.responsibleAccount = i.responsibleAccount.split(' ');
                }
            });
            this.form.meetingMinutesList = data;
        },
        /**
         * 校验表单
         */
        async validForm() {
            try {
                // 评审结论表单校验
                if (this.$refs.conclusionForm) {
                    await this.$refs.conclusionForm.validate();
                }
                // 会议纪要表单校验
                await this.$refs.meetingMinutesForm.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 重置
         * @param {Object} done 调用该函数关闭弹窗
         */
        reset(done) {
            this.showWrongMessage = false;
            this.$refs.conclusionForm &&
                this.$refs.conclusionForm.resetFields();
            this.$refs.conclusionForm &&
                this.$refs.meetingMinutesForm.resetFields();
            done();
        },
        /**
         * 校验文件大小与类型
         * @param {Object} file 文件
         * @returns {Boolean} 是否通过校验
         */
        validateExcelFile(file) {
            const isExcel =
                file.type ===
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';

            if (!isExcel) {
                this.$message.error('只能上传Excel文件!');
                return false;
            }

            const maxSize = 2 * 1024 * 1024;
            if (file.size > maxSize) {
                this.$message.error('文件大小不能超过2MB!');
                return false;
            }

            return true;
        },
        /**
         * 上传成功后的处理
         * @param {Object} response 响应
         */
        handleUploadSuccess(response) {
            if (response.head.code !== '000000') {
                this.$message.error(response.head.message);
                return;
            }
            this.$message.success('文件上传成功! 请确认无误后保存');

            response.body.forEach((i) => {
                // 校验提出人
                const isCreatorInclude = this.meetingPeopleOptions.some(
                    (j) => j.loginName === i.creatorAccount
                );
                if (!isCreatorInclude) {
                    if (!i.errMsgList) i.errMsgList = [];
                    i.creatorAccount = '';
                    i.errMsgList.push('【提出人】不存在于参会人员中！');
                }
                i.creatorAccount = i.creatorAccount ? [i.creatorAccount] : '';

                i.responsibleAccount = i.responsibleAccount
                    ? [i.responsibleAccount]
                    : '';
            });

            this.form.meetingMinutesList = [
                ...this.form.meetingMinutesList,
                ...response.body
            ];
            if (response.body[0].errMsgList) {
                this.showWrongMessage = true;
            }
        },
        /**
         * 上传错误的处理
         * @param {String} err 错误信息
         */
        handleUploadError(err) {
            this.$message.error('文件上传失败,请重试!');
            console.error('上传错误:', err);
        },
        /**
         * 上传失败接口返回的每行错误信息
         * @param {Array} err 错误信息
         * @returns {String} 每行错误信息
         */
        handlerrorMessage(err) {
            if (!err) return '';
            return err.join(' ');
        },
        /**
         * 选择结论之后的回调
         */
        handleConclusionInput() {
            if (
                this.form.meetingConclusion === '通过' &&
                this.form.meetingConclusionView === ''
            ) {
                this.form.meetingConclusionView = '评审通过无异议';
            } else {
                this.form.meetingConclusionView = '';
            }
        },
        /**
         * 责任人变更处理
         * @param {Number} index 行索引
         * @param {Array} value 选中的责任人
         */
        handleResponsibleChange(index, value) {
            const row = this.form.meetingMinutesList[index];
            if (value && value.length > 0) {
                // 责任人选择后，状态默认为"未完成"
                row.finishStatus = '未完成';
            } else {
                // 责任人取消时，清空计划完成时间和状态
                row.planFinishDate = '';
                row.finishStatus = '';
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.wrap {
    flex-wrap: wrap;
}
.meeting-material {
    margin-bottom: 12px;
}

.meeting-conclusion {
    background-color: white;
    width: 100%;
    color: black;
    padding: 15px 0;
}

.uploader {
    margin-left: auto;
    margin-right: 10px;
    margin-bottom: 10px;
}
.add-minutes {
    margin-bottom: 10px;
}

.minutes-form {
    ::v-deep .el-table__row > td {
        padding: 0px;
    }
    ::v-deep .cell {
        padding: 0 !important;
    }
    .el-form-item {
        margin-bottom: 0px;
    }
    ::v-deep .el-form-item__error {
        top: calc(50% - 10px);
        left: 16px;
    }
    ::v-deep .el-form-item__content {
        margin: 0 !important;
    }

    // 令输入框无边框
    ::v-deep .el-input__inner {
        border: none !important;
    }

    // 令输入框无边框
    ::v-deep .el-textarea__inner {
        border: none;
        resize: none;
    }
    // 令鼠标移入之后不变色
    ::v-deep .el-table tbody tr:hover > td {
        background-color: #fff !important;
    }

    .minutes-table {
        border: 1px solid #8c8c8c !important;
    }
}
.required {
    margin-right: 2px;
}
.tooltip {
    cursor: pointer;
    margin-left: 5px;
}
::v-deep .meeting-conclusion .el-form-item__label {
    font-weight: bold;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
    padding-right: 10px;
    width: fit-content;
}
.review-form {
    margin-left: 40px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
